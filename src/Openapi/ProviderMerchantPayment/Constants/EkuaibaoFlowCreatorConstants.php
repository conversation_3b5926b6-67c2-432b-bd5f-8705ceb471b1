<?php

namespace Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants;

class EkuaibaoFlowCreatorConstants
{
    const MNS_MESSAGE_TAG = 'approve-order-settle';
    /**
     * 单据类型-对公付款单
     */
    const  FLOW_TYPE_CORPORATE_PAYMENT = 1;
    /**
     * 单据类型-预付单
     */
    const  FLOW_TYPE_PREPAYMENT = 2;
    /**
     * 单据类型-核销单
     */
    const  FLOW_TYPE_RECONCILIATION = 3;

    /**
     * 申请类型-订单结算
     */
    const APPLICATION_TYPE_ORDER_SETTLE = 1;
    /**
     * 申请类型-预存
     */
    const APPLICATION_TYPE_PREPAYMENT = 2;

    /**
     * 步骤-提交预存
     */
    const STEP_SUBMIT_PREPAYMENT = 1;
    /**
     * 步骤-驳回预存
     */
    const STEP_REJECT_PREPAYMENT = 2;
    /**
     * 步骤-生成核销单
     */
    const STEP_CREATE_RECONCILIATION = 3;
    /**
     * 步骤-驳回核销单
     */
    const STEP_REJECT_INVOICE_RECONCILIATION = 4;
    /**
     * 步骤-供应商专员审核结算申请
     */
    const STEP_AUDIT_SETTLEMENT_BY_SUPPLIER_SPECIALIST = 5;
    /**
     * 步骤-生成结算申请的核销单
     */
    const STEP_CREATE_SETTLEMENT_RECONCILIATION = 6;
    /**
     * 步骤-驳回结算申请的核销单
     */
    const STEP_REJECT_SETTLEMENT_RECONCILIATION = 7;
}