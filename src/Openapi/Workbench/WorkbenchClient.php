<?php

namespace Yanqu\YanquPhplib\Openapi\Workbench;

use Yanqu\YanquPhplib\Curl\CurlUtil;
use Yanqu\YanquPhplib\Configuration;

class WorkbenchClient
{
    //获得pdf的页数
    /**
     * @param $pdfUrl
     * @return array{
     *     number:int,
     *     isFileUploadAllowed:bool,
     *     maxPdfUploadPages:int
     * }
     * @desc number 页数 isFileUploadAllow 是否能够上传到ai解析服务 maxPdfUploadPages 最大允许上传页数
     * @throws \Yanqu\YanquPhplib\Exception\CurlException
     */
    public static function getNumberOfPdfPages($pdfUrl) {
        $url = Configuration::getInstance()->get('openapi_url') . '/workbench/get-number-of-pages';
        $resJson = CurlUtil::postForm($url, [
            "url" => $pdfUrl
        ]);

        $res = json_decode($resJson, true);
        if ($res['status_code'] == 0) {
            return $res['data'];
        } else {
            throw new \Exception($res['status_msg']);
        }
    }
}
