<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2025 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/1/5 18:20
 */
namespace Yanqu\YanquPhplib\Openapi\OrderVas;

use Yanqu\YanquPhplib\Openapi\OrderVas\Constants\OrderVasPropConstants;

class OrderVasHelper
{
    /**
     * 获取增值服务分组类型
     *
     * @param string $vasUuid 增值服务唯一标识
     * @return int|null
     */
    public static function getVasGroupType($vasUuid)
    {
        $groupType = null;

        foreach (OrderVasPropConstants::SERVICE_PRICE_GROUP as $groupType => $groupUuids) {
            if (in_array($vasUuid, $groupUuids)) {
                $groupType = $groupType;
                break;
            }
        }

        return $groupType;
    }
}