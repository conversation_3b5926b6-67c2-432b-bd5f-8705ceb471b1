<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2024 (https://www.shiyanjia.com)
 * <AUTHOR> 2024/12/6 11:01
 */
namespace Yanqu\YanquPhplib\Openapi\OrderVas\Constants;

/**
 * 订单增值服务相关属性常量类
 */
class OrderVasPropConstants
{
    // 安心测唯一标识服务码
    const SERVICE_ANXINCE_UUID = 'b1b2d0764405be1afc58a227a4d0a750';

    // 极致测唯一标识服务码
    const SERVICE_JIZHICE_UUID = 'f051ec4d8029377afe5b2215f0950821';

    // 闪测（原极致测）唯一标识服务码
    const SERVICE_FLASH_TEST_UUID = 'f051ec4d8029377afe5b2215f0950821';

    // 样品宝唯一标识服务码
    const SERVICE_SAMPLE_INSURANCE_UUID = '4307ceef0277932edc8ecc1dfcd34a51';

    // 当前可用增值服务唯一标识服务码列表
    const SERVICE_UUIDS = [
        self::SERVICE_ANXINCE_UUID,
        self::SERVICE_FLASH_TEST_UUID,
        self::SERVICE_SAMPLE_INSURANCE_UUID,
    ];

    // 服务名称映射
    const SERVICE_NAME_MAP = [
        self::SERVICE_ANXINCE_UUID => '安心测',
        self::SERVICE_FLASH_TEST_UUID => '闪测',
        self::SERVICE_SAMPLE_INSURANCE_UUID => '样品宝',
    ];

    // 服务短名称映射
    const SERVICE_SHORT_NAME_MAP = [
        self::SERVICE_ANXINCE_UUID => '安',
        self::SERVICE_FLASH_TEST_UUID => '闪',
        self::SERVICE_SAMPLE_INSURANCE_UUID => '宝',
    ];

    // 服务价格分组类型：加急费
    const PRICE_GROUP_TYPE_JIAJI = 1;

    // 服务价格分组类型：样品宝费用
    const PRICE_GROUP_TYPE_SAMPLE_INSURANCE = 2;

    /**
     * 服务价格分组
     */
    const SERVICE_PRICE_GROUP = [
        self::PRICE_GROUP_TYPE_JIAJI => [
            self::SERVICE_ANXINCE_UUID,
            self::SERVICE_FLASH_TEST_UUID
        ],
        self::PRICE_GROUP_TYPE_SAMPLE_INSURANCE => [
            self::SERVICE_SAMPLE_INSURANCE_UUID
        ]
    ];
}