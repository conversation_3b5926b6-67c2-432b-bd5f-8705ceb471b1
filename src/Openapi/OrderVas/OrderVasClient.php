<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2025 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/2/20 16:16
 */

namespace Yanqu\YanquPhplib\Openapi\OrderVas;

use Yanqu\YanquPhplib\Configuration;
use Yanqu\YanquPhplib\Curl\CurlUtil;

class OrderVasClient
{
    /**
     * 获取订单样品宝服务状态
     * @param $oids
     * @return array|mixed
     */
    public static function getSampleInsuranceServiceStatus($oids)
    {
        if (empty($oids)) {
            return [];
        }

        return self::sendOrderVasRequest(
            '/order-vas/get-sample-insurance-service-status',
            ['oids' => $oids]
        );
    }

    /**
     * 获取已生效的订单增值服务信息
     * @param $oids
     * @return mixed
     * @throws \Yanqu\YanquPhplib\Exception\CurlException
     */
    public static function getEnabledOrdersVas($oids)
    {
        if (empty($oids)) {
            return [];
        }

        return self::sendOrderVasRequest(
            '/order-vas/get-enabled-orders-vas',
            ['oids' => $oids]
        );
    }

    /**
     * 检查闪测是否受用户填写的测试内容限制
     * @param $buffetId
     * @param $addressUuid
     * @param $basicValues
     * @param $advancedValues
     * @return mixed
     * @throws \Yanqu\YanquPhplib\Exception\CurlException
     */
    public static function isFlashTestRestricted($buffetId, $addressUuid, $basicValues, $advancedValues)
    {
        $requestResult = self::sendOrderVasRequest(
            '/order-vas/is-flash-test-restricted',
            [
                'buffet_id' => $buffetId,
                'address_uuid' => $addressUuid,
                'basic_values' => $basicValues,
                'advanced_values' => $advancedValues
            ]
        );

        return $requestResult['is_restricted'];
    }

    /**
     * 发送增值服务相关请求
     * @param TodoParam $todo
     * @param $queryPath
     * @return mixed
     * @throws TodoException
     * @throws \Yanqu\YanquPhplib\Exception\CurlException
     */
    private static function sendOrderVasRequest($queryPath, $params)
    {
        $requestApi = Configuration::getInstance()->get('openapi_url') . $queryPath;

        $requestResult = CurlUtil::postJson($requestApi, $params);
        $requestResult = json_decode($requestResult, true);
        if ($requestResult['status_code'] == '0') {
            return $requestResult['data'];
        } else {
            throw new OrderVasException(
                isset($requestResult['status_msg'])
                    ? $requestResult['status_msg']
                    : "订单增值服务信息请求异常：{$queryPath}"
            );
        }
    }
}