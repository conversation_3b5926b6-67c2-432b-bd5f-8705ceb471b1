<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2020 - 2020 (http://www.shiyanjia.com)
 * <AUTHOR> 2023/9/6 10:14
 */
namespace Yanqu\YanquPhplib\YqLog;

class YqLogModules
{
    // 默认模块
    const DEFAULT_MODULE = "module:common";

    /**
     * 用户增长
     */

    // 用户服务
    const USER_SERVICE = "user:service";

    // 客户关系管理
    const USER_RELATIONSHIP = "user:relationship";

    // 营销
    const USER_MATKETING = "user:marketing";

    // 团体
    const USER_GROUP = "user:group";

    // 兼职生
    const USER_PARTTIME = "user:parttime";

    /**
     * 履约
     */

    /**
     * 成长业务
     */

    /**
     * 纳米商城
     */

    /**
     * 财务
     */

    // 支付
    const FINANCE_PAY = "finance:pay";

    // 开票
    const FINANCE_INVOICE = "finance:invoice";

    // 回款
    const FINANCE_REPAYMENT = "finance:repayment";

    /**
     * 智能外呼
     */
    const SMART_CALL = "smartcall:service";

    /**
     * 其他
     */
    const OTHERS = "module:others";
}