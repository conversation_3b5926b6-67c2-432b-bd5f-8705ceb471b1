<?php

namespace Yanqu\YanquPhplib\SmartCall\Examples;

use Yanqu\YanquPhplib\SmartCall\SmartCallClient;
use Yanqu\YanquPhplib\SmartCall\Param\MakeCallParam;
use Yanqu\YanquPhplib\SmartCall\Param\QueryCallStatusParam;
use Yanqu\YanquPhplib\SmartCall\Constants\CallTypeConstants;
use Yanqu\YanquPhplib\SmartCall\Exception\SmartCallException;
use Yanqu\YanquPhplib\SmartCall\Auth\SmartCallAuth;
use Yanqu\YanquPhplib\SmartCall\Crypto\SmartCallAESUtil;

/**
 * 智能外呼使用示例
 */
class SmartCallExample
{
    /**
     * 发起外呼示例
     */
    public static function makeCallExample()
    {
        try {
            // 创建客户端
            $client = new SmartCallClient();
            
            // 创建外呼参数
            $param = new MakeCallParam();
            $param->setCalledNumber('***********')
                  ->setCallType(CallTypeConstants::TYPE_MARKETING)
                  ->setTemplateId('template_001')
                  ->setTemplateVars([
                      'customer_name' => '张三',
                      'product_name' => '测试产品',
                      'order_amount' => '1000.00'
                  ])
                  ->setBusinessId('order_12345')
                  ->setBusinessType('order_confirm')
                  ->setCallbackUrl('https://your-domain.com/smartcall/callback')
                  ->setMaxDuration(300)
                  ->setRetryCount(2);
            
            // 发送请求，phone字段会自动加密
            $response = $client->post('/api/call/make', $param->toArray(), ['called_number']);
            
            if ($response->isSuccess()) {
                $callData = $response->getData();
                echo "外呼发起成功！\n";
                echo "通话ID：" . $callData['call_id'] . "\n";
                echo "请求ID：" . $response->getRequestId() . "\n";
                
                return $callData['call_id'];
            }
            
        } catch (SmartCallException $e) {
            echo "外呼失败：" . $e->getMessage() . "\n";
            echo "错误码：" . $e->getCode() . "\n";
        }
        
        return null;
    }

    /**
     * 查询通话状态示例
     */
    public static function queryCallStatusExample($callId = null)
    {
        try {
            $client = new SmartCallClient();
            
            // 创建查询参数
            $queryParam = new QueryCallStatusParam();
            
            if ($callId) {
                // 查询指定通话
                $queryParam->setCallId($callId);
            } else {
                // 查询时间范围内的通话
                $queryParam->setStartTime(date('Y-m-d H:i:s', strtotime('-1 hour')))
                          ->setEndTime(date('Y-m-d H:i:s'))
                          ->setPage(1)
                          ->setPageSize(10);
            }
            
            $response = $client->get('/api/call/status', $queryParam->toArray());
            
            if ($response->isSuccess()) {
                $statusData = $response->getData();
                echo "查询成功！\n";
                
                if (isset($statusData['list'])) {
                    // 列表查询结果
                    echo "总数：" . $statusData['total'] . "\n";
                    foreach ($statusData['list'] as $call) {
                        echo "通话ID：{$call['call_id']}，状态：{$call['status']}\n";
                    }
                } else {
                    // 单个查询结果
                    echo "通话状态：" . $statusData['status'] . "\n";
                    echo "通话时长：" . $statusData['duration'] . "秒\n";
                }
            }
            
        } catch (SmartCallException $e) {
            echo "查询失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * AES加密示例
     */
    public static function aesEncryptExample()
    {
        try {
            $phone = '***********';
            $aesKey = 'db3ba78a20094d17'; // 16位密钥
            
            // 加密
            $encryptedPhone = SmartCallAESUtil::encrypt($phone, $aesKey);
            echo "原始手机号：{$phone}\n";
            echo "加密后：{$encryptedPhone}\n";
            
            // 解密
            $decryptedPhone = SmartCallAESUtil::decrypt($encryptedPhone, $aesKey);
            echo "解密后：{$decryptedPhone}\n";
            
            // MD5哈希
            $phoneHash = SmartCallAESUtil::md5Hash($phone);
            echo "MD5哈希：{$phoneHash}\n";
            
            // 批量加密
            $data = [
                'phone' => '***********',
                'name' => '张三',
                'email' => '<EMAIL>'
            ];
            
            $encryptedData = SmartCallAESUtil::encryptFields($data, ['phone'], $aesKey);
            echo "批量加密结果：\n";
            print_r($encryptedData);
            
        } catch (SmartCallException $e) {
            echo "加密操作失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 签名验证示例（用于处理回调）
     */
    public static function verifyCallbackExample()
    {
        // 模拟回调数据
        $appSecret = 'your_app_secret';
        $timestamp = SmartCallAuth::getMillisecondTimestamp();
        $requestBody = json_encode([
            'call_id' => 'call_12345',
            'status' => 2,
            'duration' => 120,
            'business_id' => 'order_12345'
        ]);
        
        try {
            // 生成签名（模拟外呼系统）
            $signature = SmartCallAuth::generateSignature($appSecret, $timestamp, '', $requestBody);
            
            echo "模拟回调验证：\n";
            echo "时间戳：{$timestamp}\n";
            echo "签名：{$signature}\n";
            
            // 验证签名（业务系统接收回调时的处理）
            $isValid = SmartCallAuth::verifySignature($signature, $appSecret, $timestamp, '', $requestBody);
            $isTimeValid = SmartCallAuth::isTimestampValid($timestamp);
            
            if ($isValid && $isTimeValid) {
                echo "签名验证通过！\n";
                $callbackData = json_decode($requestBody, true);
                echo "回调数据：\n";
                print_r($callbackData);
            } else {
                echo "签名验证失败！\n";
            }
            
        } catch (SmartCallException $e) {
            echo "签名验证异常：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 完整的外呼流程示例
     */
    public static function completeFlowExample()
    {
        echo "=== 智能外呼完整流程示例 ===\n\n";
        
        // 1. 发起外呼
        echo "1. 发起外呼...\n";
        $callId = self::makeCallExample();
        echo "\n";
        
        if ($callId) {
            // 2. 等待一段时间后查询状态
            echo "2. 查询通话状态...\n";
            sleep(2); // 实际使用中可能需要更长时间
            self::queryCallStatusExample($callId);
            echo "\n";
        }
        
        // 3. AES加密演示
        echo "3. AES加密演示...\n";
        self::aesEncryptExample();
        echo "\n";
        
        // 4. 签名验证演示
        echo "4. 签名验证演示...\n";
        self::verifyCallbackExample();
        echo "\n";
        
        echo "=== 示例完成 ===\n";
    }
}

// 如果直接运行此文件，执行完整示例
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    SmartCallExample::completeFlowExample();
}
