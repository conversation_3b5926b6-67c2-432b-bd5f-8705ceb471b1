<?php

namespace Yanqu\YanquPhplib\SmartCall\Crypto;

use Yanqu\YanquPhplib\SmartCall\Exception\SmartCallException;

/**
 * 智能外呼AES加密工具
 */
class SmartCallAESUtil
{
    /**
     * AES加密算法
     */
    const CIPHER_METHOD = 'AES-128-ECB';

    /**
     * AES加密
     * @param string $data 待加密数据
     * @param string $password 密钥（16位）
     * @return string Base64编码的加密结果
     * @throws SmartCallException
     */
    public static function encrypt($data, $password)
    {
        if (empty($data)) {
            throw SmartCallException::invalidParam('data');
        }
        
        if (empty($password)) {
            throw SmartCallException::invalidParam('password');
        }
        
        // 验证密钥长度（必须是16位）
        if (strlen($password) !== 16) {
            throw new SmartCallException('AES密钥长度必须为16位', SmartCallException::CODE_INVALID_PARAM);
        }
        
        // 生成AES密钥
        $key = self::generateAESKey($password);
        
        // 执行加密
        $encrypted = openssl_encrypt($data, self::CIPHER_METHOD, $key, OPENSSL_RAW_DATA);
        
        if ($encrypted === false) {
            throw new SmartCallException('AES加密失败', SmartCallException::CODE_API_FAILED);
        }
        
        return base64_encode($encrypted);
    }

    /**
     * AES解密
     * @param string $encryptedData Base64编码的加密数据
     * @param string $password 密钥（16位）
     * @return string 解密后的原始数据
     * @throws SmartCallException
     */
    public static function decrypt($encryptedData, $password)
    {
        if (empty($encryptedData)) {
            throw SmartCallException::invalidParam('encryptedData');
        }
        
        if (empty($password)) {
            throw SmartCallException::invalidParam('password');
        }
        
        // 验证密钥长度
        if (strlen($password) !== 16) {
            throw new SmartCallException('AES密钥长度必须为16位', SmartCallException::CODE_INVALID_PARAM);
        }
        
        // 生成AES密钥
        $key = self::generateAESKey($password);
        
        // Base64解码
        $encrypted = base64_decode($encryptedData);
        if ($encrypted === false) {
            throw new SmartCallException('Base64解码失败', SmartCallException::CODE_INVALID_PARAM);
        }
        
        // 执行解密
        $decrypted = openssl_decrypt($encrypted, self::CIPHER_METHOD, $key, OPENSSL_RAW_DATA);
        
        if ($decrypted === false) {
            throw new SmartCallException('AES解密失败', SmartCallException::CODE_API_FAILED);
        }
        
        return $decrypted;
    }

    /**
     * 生成MD5哈希
     * @param string $data 待哈希的数据
     * @return string MD5哈希值
     */
    public static function md5Hash($data)
    {
        return md5($data);
    }

    /**
     * 批量加密字段
     * @param array $data 数据数组
     * @param array $encryptFields 需要加密的字段名数组
     * @param string $password 加密密钥
     * @return array 加密后的数据数组
     * @throws SmartCallException
     */
    public static function encryptFields(array $data, array $encryptFields, $password)
    {
        $result = $data;
        
        foreach ($encryptFields as $field) {
            if (isset($result[$field]) && !empty($result[$field])) {
                $result[$field] = self::encrypt($result[$field], $password);
            }
        }
        
        return $result;
    }

    /**
     * 批量解密字段
     * @param array $data 数据数组
     * @param array $decryptFields 需要解密的字段名数组
     * @param string $password 解密密钥
     * @return array 解密后的数据数组
     * @throws SmartCallException
     */
    public static function decryptFields(array $data, array $decryptFields, $password)
    {
        $result = $data;
        
        foreach ($decryptFields as $field) {
            if (isset($result[$field]) && !empty($result[$field])) {
                try {
                    $result[$field] = self::decrypt($result[$field], $password);
                } catch (SmartCallException $e) {
                    // 解密失败时保持原值，可能该字段未加密
                    continue;
                }
            }
        }
        
        return $result;
    }

    /**
     * 生成AES密钥
     * 模拟Java中SecureRandom的行为
     * @param string $password 原始密码
     * @return string 生成的密钥
     */
    private static function generateAESKey($password)
    {
        // 使用SHA1算法生成种子
        $seed = sha1($password, true);
        
        // 截取前16字节作为AES-128密钥
        return substr($seed, 0, 16);
    }

    /**
     * 验证手机号格式并返回MD5值
     * @param string $phone 手机号
     * @return string 手机号的MD5值
     * @throws SmartCallException
     */
    public static function validateAndHashPhone($phone)
    {
        // 简单的手机号格式验证
        if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
            throw new SmartCallException("手机号格式错误: {$phone}", SmartCallException::CODE_INVALID_PHONE);
        }
        
        return self::md5Hash($phone);
    }
}
