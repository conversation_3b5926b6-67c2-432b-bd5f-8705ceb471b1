<?php

namespace Yanqu\YanquPhplib\SmartCall;

use Yanqu\YanquPhplib\Configuration;
use Yanqu\YanquPhplib\Curl\CurlUtil;
use Yanqu\YanquPhplib\SmartCall\Auth\SmartCallAuth;
use Yanqu\YanquPhplib\SmartCall\Crypto\SmartCallAESUtil;
use Yanqu\YanquPhplib\SmartCall\Exception\SmartCallException;
use Yanqu\YanquPhplib\SmartCall\Response\SmartCallResponse;
use Yanqu\YanquPhplib\YqLog\YqLog;
use Yanqu\YanquPhplib\YqLog\YqLogModules;

/**
 * 智能外呼客户端
 */
class SmartCallClient
{
    /**
     * 应用Key
     * @var string
     */
    private $appKey;

    /**
     * 应用密钥
     * @var string
     */
    private $appSecret;

    /**
     * API基础URL
     * @var string
     */
    private $baseUrl;

    /**
     * 应用类型（可选）
     * @var string|null
     */
    private $appType;

    /**
     * AES加密密钥（可选）
     * @var string|null
     */
    private $aesKey;

    /**
     * 日志记录器
     * @var \Yanqu\YanquPhplib\YqLog\YqLog
     */
    private $logger;

    /**
     * 构造函数
     * @param string|null $appKey 应用Key，为空时从配置读取
     * @param string|null $appSecret 应用密钥，为空时从配置读取
     * @param string|null $baseUrl API基础URL，为空时从配置读取
     */
    public function __construct($appKey = null, $appSecret = null, $baseUrl = null)
    {
        $config = Configuration::getInstance();

        $this->appKey = $appKey ?: $config->get('smart_call_app_key');
        $this->appSecret = $appSecret ?: $config->get('smart_call_app_secret');
        $this->baseUrl = $baseUrl ?: $config->get('smart_call_base_url');
        $this->appType = $config->get('smart_call_app_type');
        $this->aesKey = $config->get('smart_call_aes_key');

        // 验证必要配置
        if (empty($this->appKey)) {
            throw SmartCallException::configError('smart_call_app_key');
        }

        if (empty($this->appSecret)) {
            throw SmartCallException::configError('smart_call_app_secret');
        }

        if (empty($this->baseUrl)) {
            throw SmartCallException::configError('smart_call_base_url');
        }

        $this->logger = YqLog::logger(YqLogModules::SMART_CALL);
    }

    /**
     * 发送GET请求
     * @param string $endpoint 接口端点
     * @param array $params 查询参数
     * @return SmartCallResponse
     * @throws SmartCallException
     */
    public function get($endpoint, array $params = [])
    {
        $url = $this->buildUrl($endpoint);

        // 处理查询参数
        $requestQuery = SmartCallAuth::buildRequestQuery($params);

        // 生成请求头
        $headers = SmartCallAuth::generateHeaders(
            $this->appKey,
            $this->appSecret,
            $requestQuery,
            '',
            $this->appType
        );

        // 记录请求日志
        $this->logger->info('Smart call GET request', [
            'url' => $url,
            'params' => $params,
            'headers' => $this->maskSensitiveHeaders($headers)
        ]);

        try {
            // 发送请求 - 注意：CurlUtil::get可能不支持自定义headers，这里需要根据实际情况调整
            if (!empty($params)) {
                $url .= '?' . http_build_query($params);
            }
            $response = $this->sendCurlRequest('GET', $url, null, $headers);

            return $this->handleResponse($response, $endpoint, 'GET');

        } catch (\Exception $e) {
            $this->logger->error('Smart call GET request failed', [
                'url' => $url,
                'params' => $params,
                'error' => $e->getMessage()
            ]);

            throw SmartCallException::networkError($e->getMessage());
        }
    }

    /**
     * 发送POST请求
     * @param string $endpoint 接口端点
     * @param array $data 请求数据
     * @param array $encryptFields 需要加密的字段（可选）
     * @return SmartCallResponse
     * @throws SmartCallException
     */
    public function post($endpoint, array $data = [], array $encryptFields = [])
    {
        $url = $this->buildUrl($endpoint);

        // 处理敏感字段加密
        if (!empty($encryptFields) && !empty($this->aesKey)) {
            $data = SmartCallAESUtil::encryptFields($data, $encryptFields, $this->aesKey);
        }

        // 转换为JSON
        $requestBody = json_encode($data, JSON_UNESCAPED_UNICODE);

        // 生成请求头
        $headers = SmartCallAuth::generateHeaders(
            $this->appKey,
            $this->appSecret,
            '',
            $requestBody,
            $this->appType
        );

        // 记录请求日志
        $this->logger->info('Smart call POST request', [
            'url' => $url,
            'data' => $this->maskSensitiveData($data),
            'headers' => $this->maskSensitiveHeaders($headers)
        ]);

        try {
            // 发送请求
            $response = CurlUtil::postJson($url, $data, $headers);

            return $this->handleResponse($response, $endpoint, 'POST');

        } catch (\Exception $e) {
            $this->logger->error('Smart call POST request failed', [
                'url' => $url,
                'data' => $this->maskSensitiveData($data),
                'error' => $e->getMessage()
            ]);

            throw SmartCallException::networkError($e->getMessage());
        }
    }

    /**
     * 处理响应
     * @param string $response 响应内容
     * @param string $endpoint 接口端点
     * @param string $method 请求方法
     * @return SmartCallResponse
     * @throws SmartCallException
     */
    private function handleResponse($response, $endpoint, $method)
    {
        // 解析JSON响应
        $responseData = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->logger->error('Smart call response JSON decode failed', [
                'endpoint' => $endpoint,
                'method' => $method,
                'response' => $response,
                'json_error' => json_last_error_msg()
            ]);

            throw SmartCallException::apiFailed('响应数据格式错误');
        }

        $smartCallResponse = new SmartCallResponse($responseData);

        // 记录响应日志
        $this->logger->info('Smart call response', [
            'endpoint' => $endpoint,
            'method' => $method,
            'success' => $smartCallResponse->isSuccess(),
            'code' => $smartCallResponse->getCode(),
            'msg' => $smartCallResponse->getMsg(),
            'requestId' => $smartCallResponse->getRequestId()
        ]);

        // 如果请求失败，抛出异常
        if (!$smartCallResponse->isSuccess()) {
            throw SmartCallException::apiFailed(
                $smartCallResponse->getErrorDescription() ?: $smartCallResponse->getMsg()
            );
        }

        return $smartCallResponse;
    }

    /**
     * 构建完整URL
     * @param string $endpoint 接口端点
     * @return string
     */
    private function buildUrl($endpoint)
    {
        return rtrim($this->baseUrl, '/') . '/' . ltrim($endpoint, '/');
    }

    /**
     * 屏蔽敏感请求头信息
     * @param array $headers 请求头
     * @return array
     */
    private function maskSensitiveHeaders(array $headers)
    {
        $masked = $headers;

        if (isset($masked['X-YS-SIGNATURE'])) {
            $masked['X-YS-SIGNATURE'] = '***';
        }

        return $masked;
    }

    /**
     * 屏蔽敏感数据
     * @param array $data 数据
     * @return array
     */
    private function maskSensitiveData(array $data)
    {
        $masked = $data;
        $sensitiveFields = ['phone', 'mobile', 'telephone', 'password', 'secret'];

        foreach ($sensitiveFields as $field) {
            if (isset($masked[$field])) {
                $value = $masked[$field];
                if (is_string($value) && strlen($value) > 4) {
                    $masked[$field] = substr($value, 0, 3) . '***' . substr($value, -1);
                } else {
                    $masked[$field] = '***';
                }
            }
        }

        return $masked;
    }

    /**
     * 获取AES加密工具实例
     * @return SmartCallAESUtil
     */
    public function getAESUtil()
    {
        return new SmartCallAESUtil();
    }

    /**
     * 设置AES加密密钥
     * @param string $aesKey
     * @return $this
     */
    public function setAESKey($aesKey)
    {
        $this->aesKey = $aesKey;
        return $this;
    }

    /**
     * 设置应用类型
     * @param string $appType
     * @return $this
     */
    public function setAppType($appType)
    {
        $this->appType = $appType;
        return $this;
    }
}
