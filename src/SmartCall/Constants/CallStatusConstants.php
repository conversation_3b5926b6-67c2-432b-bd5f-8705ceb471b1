<?php

namespace Yanqu\YanquPhplib\SmartCall\Constants;

/**
 * 智能外呼通话状态常量
 */
class CallStatusConstants
{
    /**
     * 通话状态：初始化
     */
    const STATUS_INIT = 0;

    /**
     * 通话状态：呼叫中
     */
    const STATUS_CALLING = 1;

    /**
     * 通话状态：已接通
     */
    const STATUS_CONNECTED = 2;

    /**
     * 通话状态：已挂断
     */
    const STATUS_HANGUP = 3;

    /**
     * 通话状态：呼叫失败
     */
    const STATUS_FAILED = 4;

    /**
     * 通话状态：无人接听
     */
    const STATUS_NO_ANSWER = 5;

    /**
     * 通话状态：用户拒接
     */
    const STATUS_REJECTED = 6;

    /**
     * 通话状态：号码无效
     */
    const STATUS_INVALID_NUMBER = 7;

    /**
     * 状态名称映射
     */
    const STATUS_MAP = [
        self::STATUS_INIT => '初始化',
        self::STATUS_CALLING => '呼叫中',
        self::STATUS_CONNECTED => '已接通',
        self::STATUS_HANGUP => '已挂断',
        self::STATUS_FAILED => '呼叫失败',
        self::STATUS_NO_ANSWER => '无人接听',
        self::STATUS_REJECTED => '用户拒接',
        self::STATUS_INVALID_NUMBER => '号码无效',
    ];

    /**
     * 获取状态名称
     * @param int $status
     * @return string
     */
    public static function getStatusName($status)
    {
        return isset(self::STATUS_MAP[$status]) ? self::STATUS_MAP[$status] : '未知状态';
    }

    /**
     * 判断是否为终态
     * @param int $status
     * @return bool
     */
    public static function isFinalStatus($status)
    {
        return in_array($status, [
            self::STATUS_HANGUP,
            self::STATUS_FAILED,
            self::STATUS_NO_ANSWER,
            self::STATUS_REJECTED,
            self::STATUS_INVALID_NUMBER
        ]);
    }
}
