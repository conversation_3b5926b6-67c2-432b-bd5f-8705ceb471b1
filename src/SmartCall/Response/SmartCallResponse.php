<?php

namespace Yanqu\YanquPhplib\SmartCall\Response;

/**
 * 智能外呼响应基类
 */
class SmartCallResponse
{
    /**
     * 请求唯一ID
     * @var string
     */
    private $requestId;

    /**
     * 错误码
     * @var int
     */
    private $code;

    /**
     * 错误信息
     * @var string
     */
    private $msg;

    /**
     * 响应数据
     * @var mixed
     */
    private $data;

    /**
     * 原始响应数据
     * @var array
     */
    private $rawResponse;

    /**
     * 构造函数
     * @param array $response 原始响应数据
     */
    public function __construct(array $response)
    {
        $this->rawResponse = $response;
        $this->requestId = isset($response['requestId']) ? $response['requestId'] : '';
        $this->code = isset($response['code']) ? (int)$response['code'] : 0;
        $this->msg = isset($response['msg']) ? $response['msg'] : '';
        $this->data = isset($response['data']) ? $response['data'] : null;
    }

    /**
     * 判断请求是否成功
     * @return bool
     */
    public function isSuccess()
    {
        return $this->code === 200;
    }

    /**
     * 获取请求ID
     * @return string
     */
    public function getRequestId()
    {
        return $this->requestId;
    }

    /**
     * 获取错误码
     * @return int
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * 获取错误信息
     * @return string
     */
    public function getMsg()
    {
        return $this->msg;
    }

    /**
     * 获取响应数据
     * @return mixed
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * 获取原始响应数据
     * @return array
     */
    public function getRawResponse()
    {
        return $this->rawResponse;
    }

    /**
     * 获取错误描述
     * @return string
     */
    public function getErrorDescription()
    {
        $errorMap = [
            1037 => '接口调用频率超限：每秒不能超过100次',
            1038 => '接口调用频率超限：每十分钟不能超过30000次',
        ];

        if (isset($errorMap[$this->code])) {
            return $errorMap[$this->code];
        }

        return $this->msg;
    }

    /**
     * 转换为数组
     * @return array
     */
    public function toArray()
    {
        return [
            'requestId' => $this->requestId,
            'code' => $this->code,
            'msg' => $this->msg,
            'data' => $this->data,
            'success' => $this->isSuccess()
        ];
    }

    /**
     * 转换为JSON字符串
     * @return string
     */
    public function toJson()
    {
        return json_encode($this->toArray(), JSON_UNESCAPED_UNICODE);
    }
}
