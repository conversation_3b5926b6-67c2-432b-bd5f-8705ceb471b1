# 智能外呼组件使用说明

## 概述

智能外呼组件是一个通用的PHP库，用于集成智能外呼系统的API调用。该组件提供了完整的认证、加密、请求处理和响应解析功能。

## 功能特性

- ✅ **认证签名**：支持HMAC-SHA256签名算法
- ✅ **AES加密**：支持敏感字段的AES加密/解密
- ✅ **统一响应**：标准化的响应处理
- ✅ **错误处理**：完善的异常处理机制
- ✅ **日志记录**：详细的请求和响应日志
- ✅ **配置管理**：灵活的配置方式

## 安装配置

### 1. 配置文件设置

在您的配置文件中添加以下配置项：

```ini
; 智能外呼配置
smart_call_app_key = "your_app_key"
smart_call_app_secret = "your_app_secret"
smart_call_base_url = "https://api.smartcall.com"
smart_call_app_type = "your_app_type"  ; 可选
smart_call_aes_key = "your_16_char_key"  ; 可选，16位AES密钥
```

### 2. 日志模块

组件已自动添加了智能外呼日志模块：`YqLogModules::SMART_CALL`

## 基本使用

### 1. 创建客户端实例

```php
use Yanqu\YanquPhplib\SmartCall\SmartCallClient;

// 使用配置文件中的设置
$client = new SmartCallClient();

// 或者手动指定参数
$client = new SmartCallClient('your_app_key', 'your_app_secret', 'https://api.smartcall.com');
```

### 2. 发起外呼

```php
use Yanqu\YanquPhplib\SmartCall\Param\MakeCallParam;
use Yanqu\YanquPhplib\SmartCall\Constants\CallTypeConstants;

// 创建外呼参数
$param = new MakeCallParam();
$param->setCalledNumber('***********')
      ->setCallType(CallTypeConstants::TYPE_MARKETING)
      ->setTemplateId('template_001')
      ->setTemplateVars(['name' => '张三', 'product' => '测试产品'])
      ->setBusinessId('order_12345')
      ->setBusinessType('order_confirm')
      ->setCallbackUrl('https://your-domain.com/callback')
      ->setMaxDuration(300);

try {
    // 发送请求（phone字段会自动加密）
    $response = $client->post('/api/call/make', $param->toArray(), ['called_number']);

    if ($response->isSuccess()) {
        $callData = $response->getData();
        echo "外呼发起成功，通话ID：" . $callData['call_id'];
    }
} catch (SmartCallException $e) {
    echo "外呼失败：" . $e->getMessage();
}
```

### 3. 查询通话状态

```php
use Yanqu\YanquPhplib\SmartCall\Param\QueryCallStatusParam;

// 创建查询参数
$queryParam = new QueryCallStatusParam();
$queryParam->setCallId('call_12345')
           ->setPage(1)
           ->setPageSize(20);

try {
    $response = $client->get('/api/call/status', $queryParam->toArray());

    if ($response->isSuccess()) {
        $statusData = $response->getData();
        echo "通话状态：" . $statusData['status'];
    }
} catch (SmartCallException $e) {
    echo "查询失败：" . $e->getMessage();
}
```

## 高级功能

### 1. AES加密工具

```php
use Yanqu\YanquPhplib\SmartCall\Crypto\SmartCallAESUtil;

// 加密手机号
$phone = '***********';
$aesKey = 'your_16_char_key';
$encryptedPhone = SmartCallAESUtil::encrypt($phone, $aesKey);

// 解密
$decryptedPhone = SmartCallAESUtil::decrypt($encryptedPhone, $aesKey);

// 批量加密字段
$data = ['phone' => '***********', 'name' => '张三'];
$encryptedData = SmartCallAESUtil::encryptFields($data, ['phone'], $aesKey);
```

### 2. 签名验证

```php
use Yanqu\YanquPhplib\SmartCall\Auth\SmartCallAuth;

// 验证回调签名
$signature = $_SERVER['HTTP_X_YS_SIGNATURE'];
$timestamp = $_SERVER['HTTP_X_YS_TIME'];
$requestBody = file_get_contents('php://input');

$isValid = SmartCallAuth::verifySignature(
    $signature,
    'your_app_secret',
    $timestamp,
    '',
    $requestBody
);

if ($isValid && SmartCallAuth::isTimestampValid($timestamp)) {
    // 签名验证通过，处理回调数据
    $callbackData = json_decode($requestBody, true);
}
```

### 3. 常量使用

```php
use Yanqu\YanquPhplib\SmartCall\Constants\CallStatusConstants;
use Yanqu\YanquPhplib\SmartCall\Constants\CallTypeConstants;

// 获取状态名称
$statusName = CallStatusConstants::getStatusName(CallStatusConstants::STATUS_CONNECTED);

// 判断是否为终态
$isFinal = CallStatusConstants::isFinalStatus($status);

// 获取外呼类型名称
$typeName = CallTypeConstants::getTypeName(CallTypeConstants::TYPE_MARKETING);
```

## 错误处理

组件提供了完善的异常处理机制：

```php
use Yanqu\YanquPhplib\SmartCall\Exception\SmartCallException;

try {
    $response = $client->post('/api/call/make', $data);
} catch (SmartCallException $e) {
    switch ($e->getCode()) {
        case SmartCallException::CODE_INVALID_PARAM:
            // 参数错误
            break;
        case SmartCallException::CODE_NETWORK_ERROR:
            // 网络错误
            break;
        case SmartCallException::CODE_API_FAILED:
            // API调用失败
            break;
        case SmartCallException::CODE_CONFIG_ERROR:
            // 配置错误
            break;
        default:
            // 其他错误
            break;
    }
}
```

## 日志记录

组件会自动记录详细的请求和响应日志，包括：

- 请求URL和参数
- 请求头信息（敏感信息会被屏蔽）
- 响应状态和数据
- 错误信息

日志模块：`smartcall:service`

## 注意事项

1. **时间戳验证**：请确保服务器时间与智能外呼系统时间差不超过5分钟
2. **签名安全**：AppSecret必须妥善保管，不要在前端代码中暴露
3. **加密密钥**：AES密钥必须是16位字符串
4. **频率限制**：注意接口调用频率限制，避免触发限流
5. **手机号格式**：确保手机号格式正确（11位，以1开头）

## 扩展开发

如需添加新的接口，可以：

1. 在对应的业务项目中创建具体的业务方法
2. 使用 `SmartCallClient` 的 `get()` 或 `post()` 方法
3. 创建对应的参数类继承基础参数处理
4. 根据需要添加新的常量定义

## 测试验证

### 运行组件测试

```php
// 运行完整测试
require_once 'src/SmartCall/Tests/SmartCallTest.php';
SmartCallTest::runAllTests();

// 或者单独测试某个功能
SmartCallTest::testSignature();     // 测试签名
SmartCallTest::testAESEncryption(); // 测试加密
SmartCallTest::testConstants();     // 测试常量
```

### 建议的测试项目

1. 签名生成的正确性
2. AES加密解密的准确性
3. 参数验证的有效性
4. 异常处理的完整性
5. 网络请求的稳定性

## 故障排除

### 常见问题

#### 1. 签名验证失败

**问题**: 收到签名错误的响应

**解决方案**:
- 检查 `appKey` 和 `appSecret` 是否正确
- 确认时间戳格式为毫秒级
- 验证服务器时间与外呼系统时间差不超过5分钟
- 检查请求参数的排序和拼接是否正确

```php
// 调试签名生成
$timestamp = SmartCallAuth::getMillisecondTimestamp();
$signature = SmartCallAuth::generateSignature($appSecret, $timestamp, $requestQuery, $requestBody);
echo "待签名字符串: {$timestamp}{$requestQuery}{$requestBody}\n";
echo "生成的签名: {$signature}\n";
```

#### 2. AES加密失败

**问题**: 加密或解密时出现错误

**解决方案**:
- 确认AES密钥长度为16位
- 检查openssl扩展是否已安装
- 验证待加密数据不为空

```php
// 检查openssl扩展
if (!extension_loaded('openssl')) {
    echo "OpenSSL扩展未安装\n";
}

// 验证密钥长度
$aesKey = 'your_key';
if (strlen($aesKey) !== 16) {
    echo "AES密钥长度必须为16位\n";
}
```

#### 3. 网络请求超时

**问题**: 请求智能外呼API时超时

**解决方案**:
- 检查网络连接
- 增加超时时间设置
- 确认API地址是否正确

```php
// 在SmartCallClient中可以调整超时设置
// 修改 sendCurlRequest 方法中的 CURLOPT_TIMEOUT 值
```

#### 4. 配置错误

**问题**: 提示配置项缺失

**解决方案**:
- 检查配置文件中是否包含所有必需的配置项
- 确认Configuration类能正确读取配置

```php
// 检查配置
$config = Configuration::getInstance();
var_dump($config->get('smart_call_app_key'));
var_dump($config->get('smart_call_app_secret'));
var_dump($config->get('smart_call_base_url'));
```

#### 5. 日志记录问题

**问题**: 没有看到相关日志

**解决方案**:
- 确认YqLog配置正确
- 检查日志级别设置
- 验证日志文件权限

```php
// 手动测试日志
use Yanqu\YanquPhplib\YqLog\YqLog;
use Yanqu\YanquPhplib\YqLog\YqLogModules;

$logger = YqLog::logger(YqLogModules::SMART_CALL);
$logger->info('测试日志记录', ['test' => 'data']);
```

### 调试技巧

1. **启用详细日志**: 在开发环境中启用DEBUG级别日志
2. **使用测试工具**: 利用提供的测试类验证各个组件
3. **分步调试**: 先测试签名和加密，再测试完整的API调用
4. **网络抓包**: 使用工具查看实际发送的HTTP请求

### 性能优化

1. **连接复用**: 在高并发场景下考虑使用连接池
2. **缓存配置**: 避免重复读取配置文件
3. **异步处理**: 对于非关键路径的外呼，考虑异步处理
4. **错误重试**: 实现合理的重试机制
