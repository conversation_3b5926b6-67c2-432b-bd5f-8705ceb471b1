<?php

namespace Yanqu\YanquPhplib\SmartCall\Auth;

use Yanqu\YanquPhplib\SmartCall\Exception\SmartCallException;

/**
 * 智能外呼认证签名工具
 */
class SmartCallAuth
{
    /**
     * 生成请求签名
     * @param string $appSecret 应用密钥
     * @param int $timestamp 时间戳（毫秒）
     * @param string $requestQuery 请求查询字符串（GET请求的参数值拼接）
     * @param string $requestBody 请求体内容（POST请求的body）
     * @return string 签名字符串
     * @throws SmartCallException
     */
    public static function generateSignature($appSecret, $timestamp, $requestQuery = '', $requestBody = '')
    {
        if (empty($appSecret)) {
            throw SmartCallException::configError('appSecret');
        }

        // 待签名字符串 = 时间戳 + 请求串 + 请求体
        $needSign = $timestamp . $requestQuery . $requestBody;

        // 使用HMAC-SHA256算法生成签名
        $signature = hash_hmac('sha256', $needSign, $appSecret);

        return $signature;
    }

    /**
     * 生成请求头
     * @param string $appKey 应用Key
     * @param string $appSecret 应用密钥
     * @param string $requestQuery 请求查询字符串
     * @param string $requestBody 请求体内容
     * @param string|null $appType 应用类型（可选）
     * @return array 请求头数组
     * @throws SmartCallException
     */
    public static function generateHeaders($appKey, $appSecret, $requestQuery = '', $requestBody = '', $appType = null)
    {
        if (empty($appKey)) {
            throw SmartCallException::configError('appKey');
        }

        // 生成毫秒时间戳
        $timestamp = self::getMillisecondTimestamp();

        // 生成签名
        $signature = self::generateSignature($appSecret, $timestamp, $requestQuery, $requestBody);

        $headers = [
            'X-YS-APIKEY' => $appKey,
            'X-YS-TIME' => (string)$timestamp,
            'X-YS-SIGNATURE' => $signature,
            'Content-Type' => 'application/json'
        ];

        // 如果有应用类型，添加到请求头
        if (!empty($appType)) {
            $headers['X-YS-APPTYPE'] = $appType;
        }

        return $headers;
    }

    /**
     * 处理GET请求的查询参数
     * 对Query参数按照字典对Key进行排序后，按照value1+value2方法拼接
     * @param array $params 查询参数数组
     * @return string 拼接后的查询字符串
     */
    public static function buildRequestQuery(array $params)
    {
        if (empty($params)) {
            return '';
        }

        // 按键名排序
        ksort($params);

        $values = [];
        foreach ($params as $key => $value) {
            $values[] = $value === null ? '' : (string)$value;
        }

        return implode('', $values);
    }

    /**
     * 获取毫秒时间戳
     * @return int
     */
    public static function getMillisecondTimestamp()
    {
        $microtime = microtime(true);
        return (int)floor($microtime * 1000);
    }

    /**
     * 验证时间戳是否在有效范围内（5分钟内）
     * @param int $timestamp 毫秒时间戳
     * @return bool
     */
    public static function isTimestampValid($timestamp)
    {
        $currentTimestamp = self::getMillisecondTimestamp();
        $diff = abs($currentTimestamp - $timestamp);

        // 5分钟 = 5 * 60 * 1000 毫秒
        return $diff <= 300000;
    }

    /**
     * 验证签名
     * @param string $signature 待验证的签名
     * @param string $appSecret 应用密钥
     * @param int $timestamp 时间戳
     * @param string $requestQuery 请求查询字符串
     * @param string $requestBody 请求体内容
     * @return bool
     */
    public static function verifySignature($signature, $appSecret, $timestamp, $requestQuery = '', $requestBody = '')
    {
        try {
            $expectedSignature = self::generateSignature($appSecret, $timestamp, $requestQuery, $requestBody);
            return hash_equals($expectedSignature, $signature);
        } catch (\Exception $e) {
            return false;
        }
    }
}
