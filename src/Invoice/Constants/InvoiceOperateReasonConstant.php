<?php

namespace Yanqu\YanquPhplib\Invoice\Constants;

class InvoiceOperateReasonConstant
{
    /**
     * 发票转移
     */
    const INVOICE_TRANSFER = 1;

    /**
     * 订单需改价
     */
    const MODIFY_ORDER_PRICE = 2;

    /**
     * 要预存还款
     */
    const PREPAYMENT_REQUIRED = 3;

    /**
     * 不想开票立减
     */
    const NO_INVOICE_DISCOUNT = 4;

    /**
     * 要变更开票信息
     */
    const MODIFY_INVOICE_INFO = 5;

    /**
     * 系统问题导致开票有误
     */
    const INVOICE_ERROR = 6;

    /**
     * 要拆分/合并发票
     */
    const SPLIT_MERGE_INVOICE = 7;

    /**
     * 发票过期
     */
    const EXPIRED_INVOICE = 8;

    /**
     * 发票连号
     */
    const CONSECUTIVE_INVOICE = 9;

    /**
     * 超过报销限额，需后面再开
     */
    const EXCEED_REIMBURSEMENT_LIMIT = 10;

    /**
     * 不存了
     */
    const NO_LONGER_NEEDED = 11;

    /**
     * 修改日期
     */
    const MODIFY_DATE = 12;

    /**
     * 修改金额
     */
    const MODIFY_AMOUNT = 13;

    /**
     * 修改抬头
     */
    const MODIFY_TITLE = 14;

    /**
     * 修改主体
     */
    const MODIFY_SUBJECT = 15;

    /**
     * 修改返利方式
     */
    const MODIFY_REBATE_METHOD = 16;
    /**
     * 修改明细
     */
    const MODIFY_DETAIL = 17;

    /**
     * 折扣开票
     */
    const DISCOUNT_INVOICE = 18;

    /**
     * 修改银行账号
     */
    const MODIFY_BANK_ACCOUNT = 19;

    /**
     * 内部测试
     */
    const INNER_TEST = 20;

    /**
     * 系统主体不支持开材料费
     */
    const MATERIAL_FEE_NOT_SUPPORT = 21;

    /**
     * invoice 系统无对应币种
     */
    const NO_CURRENCY = 22;

    /**
     * 开收据
     */
    const ISSUE_RECEIPT = 23;

    /**
     *  汇率原因
     */
    const EXCHANGE_RATE = 24;

    const REASON_MAP = [
        InvoiceOperateTypeConstant::OPEN_TICKET_OFFLINE => [
            InvoiceConstants::INVOICE_APPLY_TYPE_ORDER => [
                [
                    'id' => self::INVOICE_TRANSFER,
                    'name' => '发票转移',
                ],
                [
                    'id' => self::DISCOUNT_INVOICE,
                    'name' => '折扣开票',
                ],
                [
                    'id' => self::MODIFY_BANK_ACCOUNT,
                    'name' => '修改银行账号',
                ],
                [
                    'id' => self::NO_CURRENCY,
                    'name' => '系统无对应币种',
                ],
                [
                    'id' => self::ISSUE_RECEIPT,
                    'name' => '开收据'
                ],
                [
                    'id' => self::EXCHANGE_RATE,
                    'name' => '汇率原因'
                ]
            ],
            InvoiceConstants::INVOICE_APPLY_TYPE_PRE_STORE => [
                [
                    'id' => self::INVOICE_TRANSFER,
                    'name' => '发票转移',
                ],
                [
                    'id' => self::DISCOUNT_INVOICE,
                    'name' => '折扣开票',
                ],
                [
                    'id' => self::MODIFY_BANK_ACCOUNT,
                    'name' => '修改银行账号',
                ],
                [
                    'id' => self::NO_CURRENCY,
                    'name' => '系统无对应币种',
                ],
                [
                    'id' => self::ISSUE_RECEIPT,
                    'name' => '开收据'
                ],
                [
                    'id' => self::EXCHANGE_RATE,
                    'name' => '汇率原因'
                ]
            ],
        ],
        InvoiceOperateTypeConstant::RED_FLUSH => [
            InvoiceConstants::INVOICE_APPLY_TYPE_ORDER => [
                [
                    'id' => self::MODIFY_ORDER_PRICE,
                    'name' => '订单需改价',
                ],
                [
                    'id' => self::PREPAYMENT_REQUIRED,
                    'name' => '要预存还款',
                ],
                [
                    'id' => self::NO_INVOICE_DISCOUNT,
                    'name' => '不想开票立减'
                ],
                [
                    'id' => self::MODIFY_INVOICE_INFO,
                    'name' => '要更改开票信息',
                ],
                [
                    'id' => self::INVOICE_ERROR,
                    'name' => '系统问题导致开票有误',
                ],
                [
                    'id' => self::SPLIT_MERGE_INVOICE,
                    'name' => '要拆分/合并发票',
                ],
                [
                    'id' => self::EXPIRED_INVOICE,
                    'name' => '发票过期',
                ],
                [
                    'id' => self::CONSECUTIVE_INVOICE,
                    'name' => '发票连号',
                ],
                [
                    'id' => self::EXCEED_REIMBURSEMENT_LIMIT,
                    'name' => '超过报销限额，需后面再开',
                ],
                [
                    'id' => self::INNER_TEST,
                    'name' => '内部测试',
                ],
                [
                    'id' => self::MATERIAL_FEE_NOT_SUPPORT,
                    'name' => '系统主体不支持开材料费',
                ]
            ],
            InvoiceConstants::INVOICE_APPLY_TYPE_PRE_STORE =>
                [
                    [
                        'id' => self::NO_LONGER_NEEDED,
                        'name' => '不存了',
                    ],
                    [
                        'id' => self::MODIFY_DATE,
                        'name' => '修改日期',
                    ],
                    [
                        'id' => self::MODIFY_AMOUNT,
                        'name' => '修改金额',
                    ],
                    [
                        'id' => self::MODIFY_TITLE,
                        'name' => '修改抬头',
                    ],
                    [
                        'id' => self::MODIFY_SUBJECT,
                        'name' => '修改主体',
                    ],
                    [
                        'id' => self::MODIFY_REBATE_METHOD,
                        'name' => '修改返利方式',
                    ],
                    [
                        'id' => self::MODIFY_DETAIL,
                        'name' => '修改明细',
                    ],
                    [
                        'id' => self::INNER_TEST,
                        'name' => '内部测试',
                    ]
                ],
        ]
    ];

    /**
     * 客户端退票原因列表
     */
    const SHIYANJIA_BACK_REASON_LIST
        = [
            [
                'id'   => self::PREPAYMENT_REQUIRED,
                'name' => '要预存还款',
            ],
            [
                'id'   => self::MODIFY_INVOICE_INFO,
                'name' => '要更改开票信息',
            ],
            [
                'id' => self::EXPIRED_INVOICE,
                'name' => '发票过期',
            ],
            [
                'id' => self::NO_INVOICE_DISCOUNT,
                'name' => '不想开票立减'
            ]
        ];

    /**
     * 预存申请红冲的原因id映射到操作id的映射
     */
    const APPLY_REVERSE_REASON_MAP = [
        '不存了' => self::NO_LONGER_NEEDED,
        '修改日期' => self::MODIFY_DATE,
        '修改金额' => self::MODIFY_AMOUNT,
        '修改明细' => self::MODIFY_DETAIL,
        '修改抬头' => self::MODIFY_TITLE,
        '修改主体' => self::MODIFY_SUBJECT,
        '修改返利方式' => self::MODIFY_REBATE_METHOD,
    ];

    const DESC_MAP = [
        self::INVOICE_TRANSFER => '发票转移',
        self::MODIFY_ORDER_PRICE => '订单需改价',
        self::PREPAYMENT_REQUIRED => '要预存还款',
        self::NO_INVOICE_DISCOUNT => '不想开票立减',
        self::MODIFY_INVOICE_INFO => '要变更开票信息',
        self::INVOICE_ERROR => '系统问题导致开票有误',
        self::SPLIT_MERGE_INVOICE => '要拆分/合并发票',
        self::EXPIRED_INVOICE => '发票过期',
        self::CONSECUTIVE_INVOICE => '发票连号',
        self::EXCEED_REIMBURSEMENT_LIMIT => '超过报销限额，需后面再开',
        self::NO_LONGER_NEEDED => '不存了',
        self::MODIFY_DATE => '修改日期',
        self::MODIFY_AMOUNT => '修改金额',
        self::MODIFY_TITLE => '修改抬头',
        self::MODIFY_SUBJECT => '修改主体',
        self::MODIFY_REBATE_METHOD => '修改返利方式',
        self::MODIFY_DETAIL => '修改明细',
        self::DISCOUNT_INVOICE => '折扣开票',
        self::MODIFY_BANK_ACCOUNT => '修改银行账号',
        self::INNER_TEST => '内部测试',
        self::MATERIAL_FEE_NOT_SUPPORT => '系统主体不支持开材料费',
        self::NO_CURRENCY => '系统无对应币种',
        self::ISSUE_RECEIPT => '开收据',
        self::EXCHANGE_RATE => '汇率原因',
    ];

    public static function getDesc($code)
    {
        if (array_key_exists($code, self::DESC_MAP)) {
            return self::DESC_MAP[$code];
        } else {
            return '';
        }
    }

    public static function getApplyReverseReasonMap($reasonId)
    {
        if (array_key_exists($reasonId, self::APPLY_REVERSE_REASON_MAP)){
            return self::APPLY_REVERSE_REASON_MAP[$reasonId];
        } else {
            return '';
        }
    }
}